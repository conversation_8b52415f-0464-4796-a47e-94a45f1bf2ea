//
//  DropdownMenuOverlay.swift
//  RockerSTT
//
//  Created by <PERSON><PERSON> on 2025/1/26.
//

import SwiftUI

/// Dropdown menu overlay that appears at the app level
struct DropdownMenuOverlay: View {
    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    
    var body: some View {
        if navigationCoordinator.showingDropdownMenu {
            ZStack {
                // Background overlay for tap to dismiss - use higher opacity for better touch detection
                Color.black.opacity(0.01)
                    .ignoresSafeArea()
                    .contentShape(Rectangle()) // Ensure the entire area is tappable
                    .onTapGesture {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            navigationCoordinator.showingDropdownMenu = false
                        }
                    }
                
                // Dropdown menu positioned in top-right
                VStack {
                    HStack {
                        Spacer()
                        
                        customDropdownMenu
                            .padding(.top, 48) // Position below navigation bar
                            .padding(.trailing, 16) // Right padding
                    }
                    
                    Spacer()
                }
            }
            .transition(.asymmetric(
                insertion: .scale(scale: 0.8).combined(with: .opacity),
                removal: .scale(scale: 0.9).combined(with: .opacity)
            ))
        }
    }
    
    private var customDropdownMenu: some View {
        VStack(spacing: 4) {
            // Record Tab
            menuItem(
                title: "Record",
                icon: navigationCoordinator.selectedTab == .record ? "mic.fill" : "mic",
                isSelected: navigationCoordinator.selectedTab == .record,
                badgeCount: 0
            ) {
                navigationCoordinator.navigateToTab(.record)
                HapticFeedbackManager.shared.selectionChanged()
                withAnimation(.easeInOut(duration: 0.2)) {
                    navigationCoordinator.showingDropdownMenu = false
                }
            }

            // History Tab
            menuItem(
                title: "History",
                icon: "clock.arrow.circlepath",
                isSelected: navigationCoordinator.selectedTab == .history,
                badgeCount: navigationCoordinator.historyBadgeCount
            ) {
                navigationCoordinator.navigateToTab(.history)
                HapticFeedbackManager.shared.selectionChanged()
                withAnimation(.easeInOut(duration: 0.2)) {
                    navigationCoordinator.showingDropdownMenu = false
                }
            }

            // Settings Tab
            menuItem(
                title: "Settings",
                icon: "gear",
                isSelected: navigationCoordinator.selectedTab == .settings,
                badgeCount: 0
            ) {
                navigationCoordinator.navigateToTab(.settings)
                HapticFeedbackManager.shared.selectionChanged()
                withAnimation(.easeInOut(duration: 0.2)) {
                    navigationCoordinator.showingDropdownMenu = false
                }
            }
        }
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    Color.brandOrchid.opacity(0.2),
                                    Color.brandFrenchLilac.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(
                    color: Color.brandPersianPurple.opacity(0.15),
                    radius: 12,
                    x: 0,
                    y: 4
                )
        )
        .frame(width: 164)
    }

    // MARK: - Menu Item Component

    @ViewBuilder
    private func menuItem(
        title: String,
        icon: String,
        isSelected: Bool,
        badgeCount: Int,
        action: @escaping () -> Void
    ) -> some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.spacing.xSmall) {
                // Icon with elegant background
                ZStack {
                    Circle()
                        .fill(
                            isSelected
                            ? LinearGradient(
                                colors: [Color.brandOrchid.opacity(0.2), Color.brandFrenchLilac.opacity(0.1)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                            : LinearGradient(
                                colors: [Color.clear],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 32, height: 32)

                    Image(systemName: icon)
                        .font(.system(size: 16, weight: isSelected ? .semibold : .medium))
                        .foregroundColor(isSelected ? .brandOrchid : .brandSecondary)
                }

                // Title and badge
                HStack(spacing: DesignSystem.spacing.xSmall) {
                    Text(title)
                        .font(DesignSystem.typography.subheadline)
                        .fontWeight(isSelected ? .semibold : .medium)
                        .foregroundColor(isSelected ? .brandPersianPurple : .primary)

                    if badgeCount > 0 {
                        Text("\(badgeCount > 99 ? "99+" : "\(badgeCount)")")
                            .font(.caption2)
                            .fontWeight(.bold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                Capsule()
                                    .fill(
                                        LinearGradient(
                                            colors: [Color.brandAmber, Color.brandAmber.opacity(0.8)],
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                            )
                            .shadow(
                                color: Color.brandAmber.opacity(0.3),
                                radius: 2,
                                x: 0,
                                y: 1
                            )
                    }
                }

                Spacer()

                // Selection indicator
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.brandOrchid)
                        .shadow(
                            color: Color.brandOrchid.opacity(0.3),
                            radius: 2,
                            x: 0,
                            y: 1
                        )
                        .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: DesignSystem.spacing.micro))
                }
            }
            .padding(.horizontal, DesignSystem.spacing.xSmall)
            .padding(.vertical, DesignSystem.spacing.xSmall)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.brandAlabaster.opacity(0.5) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview

#Preview {
    ZStack {
        Color.gray.opacity(0.3)
            .ignoresSafeArea()
        
        DropdownMenuOverlay()
    }
    .environmentObject(NavigationCoordinator())
}
