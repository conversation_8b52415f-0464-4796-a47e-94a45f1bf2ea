//
//  FloatingHistoryView.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/1/25.
//

import SwiftUI

/// Main floating history view container that replaces HistoryListView
/// Integrates floating tab bar with unified grid content using modern design patterns
struct FloatingHistoryView: View {

    // MARK: - Properties

    @StateObject private var viewModel = FloatingHistoryViewModel()
    @State private var scrollOffset: CGFloat = 0
    @State private var isTabBarVisible: Bool = true

    // MARK: - Navigation & Search Integration

    @EnvironmentObject private var navigationCoordinator: NavigationCoordinator
    @State private var showingSearchView = false
    @State private var showingExportOptions = false
    
    // MARK: - Design Constants
    
    private let tabBarHorizontalMargin: CGFloat = 16
    private let tabBarBottomMargin: CGFloat = 24
    
    // MARK: - Body
    
    var body: some View {
        NavigationView {
            ZStack(alignment: .bottom) {
                // Main content area
                mainContent
                
                // Floating tab bar
                floatingTabBar
            }
            .navigationTitle("Notes")
            .navigationBarTitleDisplayMode(.large)
//            .toolbarBackground(.clear, for: .navigationBar)
//            .toolbarBackground(.hidden, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 12) {
                        searchButton
                        ToolbarMenuButton()
                    }
                }
            }
            .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
            .sheet(isPresented: $showingSearchView) {
                HistorySearchView()
                    .environmentObject(navigationCoordinator)
            }
            .sheet(isPresented: $showingExportOptions) {
                ExportOptionsView(sessions: viewModel.filteredSessions)
                    .environmentObject(navigationCoordinator)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: .navigateToHistorySearch)) { _ in
            showingSearchView = true
        }
        .onReceive(NotificationCenter.default.publisher(for: .navigateToSession)) { notification in
            if let session = notification.object as? HistorySession {
                handleSessionTap(session)
            }
        }
    }
    
    // MARK: - Main Content
    
    private var mainContent: some View {
        ScrollViewReader { proxy in
            ScrollView {
                ScrollOffsetReader { scrollData in
                    scrollOffset = scrollData.offset
                    viewModel.handleScrollOffsetChange(scrollData)
                }

                if !viewModel.shouldShowEmptyState {
                    LazyHistoryGrid(
                        sessions: viewModel.filteredSessions,
                        selectedTab: viewModel.selectedTab,
                        onSessionTap: { session in
                            handleSessionTap(session)
                        },
                        onFavoriteToggle: { session in
                            viewModel.toggleFavorite(for: session)
                        },
                        onSaveToggle: { session in
                            viewModel.toggleSaved(for: session)
                        },
                        onDelete: { session in
                            viewModel.deleteSession(session)
                        },
                        onVisibleRangeChanged: { startIndex, endIndex in
                            viewModel.updateVisibleRange(startIndex: startIndex, endIndex: endIndex)
                        }
                    )
                } else {
                    FloatingHistoryEmptyStateView(
                        selectedTab: viewModel.selectedTab,
                        onAction: viewModel.selectedTab == .recents ? {
                            handleEmptyStateAction()
                        } : nil
                    )
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
                    // Add tap gesture to empty state to show hidden tab bar
                    .onTapGesture {
                        viewModel.handleTapGesture()
                    }
                }
            }
            .coordinateSpace(name: "scroll")
            .refreshable {
                await refreshData()
            }
            // Add background tap area for showing hidden tab bar
            .background(
                Color.clear
                    .contentShape(Rectangle())
                    .onTapGesture {
                        // Only handle tap if tab bar is hidden and tap is in empty area
                        if !isTabBarVisible {
                            viewModel.handleTapGesture()
                        }
                    }
            )
        }
    }
    
    // MARK: - Floating Tab Bar
    
    private var floatingTabBar: some View {
        FloatingTabBar(
            selectedTab: $viewModel.selectedTab,
            isVisible: $isTabBarVisible,
            onTabSelected: { tab in
                viewModel.selectTab(tab)
            },
            animationManager: viewModel.animationManager
        )
        .padding(.horizontal, tabBarHorizontalMargin)
        .padding(.bottom, tabBarBottomMargin)
        .onReceive(viewModel.visibilityManager.$isVisible) { visible in
            isTabBarVisible = visible
        }
    }
    
    // MARK: - Search Button
    
    private var searchButton: some View {
        Button(action: {
            showingSearchView = true
            HapticFeedbackManager.shared.buttonPressed()
        }) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 18, weight: .medium))
                .foregroundColor(DesignSystem.brandColors.persianPurple)
        }
        .accessibilityLabel("Search transcriptions")
        .accessibilityHint("Double tap to search through your transcription history")
    }


    
    // MARK: - Helper Methods
    
    /// Handles session tap navigation
    private func handleSessionTap(_ session: HistorySession) {
        // Use NavigationCoordinator for proper navigation
        navigationCoordinator.navigateToSession(session)

        // Provide haptic feedback
        HapticFeedbackManager.shared.buttonPressed()

        // Log navigation for analytics
        print("Navigating to session: \(session.title ?? "Untitled")")
    }
    
    /// Handles pull-to-refresh functionality
    private func refreshData() async {
        await withCheckedContinuation { continuation in
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                viewModel.loadSessions()
                continuation.resume()
            }
        }
    }

    /// Handles empty state action (e.g., start recording)
    private func handleEmptyStateAction() {
        // Navigate to recording tab
        navigationCoordinator.navigateToTab(.record)

        // Provide haptic feedback
        HapticFeedbackManager.shared.buttonPressed()

        print("FloatingHistory: Navigated to recording from empty state")
    }
}

// MARK: - Preview

#Preview {
    FloatingHistoryView()
        .preferredColorScheme(.light)
}

#Preview("Dark Mode") {
    FloatingHistoryView()
        .preferredColorScheme(.dark)
}

// MARK: - Demo View for Testing

/// Demo view for testing the floating history interface
struct FloatingHistoryDemoView: View {
    @StateObject private var viewModel = FloatingHistoryViewModel()
    
    var body: some View {
        VStack(spacing: 20) {
            // Tab counts display
            HStack(spacing: 20) {
                ForEach([HistoryTab.recents, .favorites, .saved], id: \.self) { tab in
                    VStack {
                        Text(tab.displayName)
                            .font(DesignSystem.typography.caption)
                            .foregroundColor(DesignSystem.brandColors.adaptiveTextSecondary)
                        
                        Text("\(viewModel.tabCounts[tab] ?? 0)")
                            .font(DesignSystem.typography.headline)
                            .foregroundColor(DesignSystem.brandColors.persianPurple)
                    }
                }
            }
            .padding()
            .background(DesignSystem.brandColors.adaptiveBackground)
            .cornerRadius(12)
            
            // Main floating history view
            FloatingHistoryView()
        }
        .padding()
        .background(DesignSystem.brandColors.adaptiveBackgroundGradient)
    }
}

#Preview("Demo") {
    FloatingHistoryDemoView()
}
