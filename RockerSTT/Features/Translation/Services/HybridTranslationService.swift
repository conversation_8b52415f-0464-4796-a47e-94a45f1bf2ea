//
//  HybridTranslationService.swift
//  RockerSTT
//
//  Created by Augment Agent on 2025/7/26.
//

import Foundation
import Combine

// MARK: - NLLB Response Models

struct NLLBTranslationResponse: Codable {
    let result: String
}

struct NLLBLanguageDetectionResponse: Codable {
    let language: String
    let confidence: Double
}

// MARK: - Hybrid Translation Service

/// Enhanced translation service that supports both Google Translate and NLLB
@MainActor
class HybridTranslationService: ObservableObject {
    
    // MARK: - Properties
    
    @Published var isTranslating = false
    @Published var selectedTargetLanguage: TranslationLanguage = .spanish {
        didSet {
            UserDefaults.standard.set(selectedTargetLanguage.rawValue, forKey: "selectedTargetLanguage")
        }
    }
    @Published var isTranslationEnabled = true {
        didSet {
            UserDefaults.standard.set(isTranslationEnabled, forKey: "isTranslationEnabled")
        }
    }
    private let currentConfig: TranslationServiceConfig = .appDefault

    private let session: URLSession
    private var cancellables = Set<AnyCancellable>()

    // Google Cloud Translation service
    private let googleCloudService: GoogleCloudTranslationService
    
    // Translation cache for performance optimization
    private var translationCache: [String: String] = [:]
    private let cacheQueue = DispatchQueue(label: "translation.cache", qos: .utility)
    
    // MARK: - Initialization
    
    init(googleCloudAPIKey: String = "") {

        // 🔐 COLD LAUNCH API KEY PROTECTION
        // Uses multiple layers: Keychain → Environment → Obfuscated embedded key
        let apiKey = googleCloudAPIKey.isEmpty ?
            SecureAPIKeyProvider.getGoogleCloudAPIKey() :
            googleCloudAPIKey

        // Initialize Google Cloud Translation service
        let googleCloudConfig = GoogleCloudTranslationService.Config(
            apiKey: apiKey,
            projectId: nil,
            baseURL: "https://translation.googleapis.com/language/translate/v2"
        )
        self.googleCloudService = GoogleCloudTranslationService(config: googleCloudConfig)

        // Configure URLSession with timeout and headers
        let sessionConfig = URLSessionConfiguration.default
        sessionConfig.timeoutIntervalForRequest = 30.0
        sessionConfig.timeoutIntervalForResource = 60.0
        sessionConfig.httpAdditionalHeaders = [
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1"
        ]
        self.session = URLSession(configuration: sessionConfig)

        // Load saved preferences
        loadSavedPreferences()

        print("🌐 HybridTranslationService: Initialized with \(currentConfig.preferredService.displayName) as primary")
        print("🔑 Google Cloud API Key: \(googleCloudAPIKey.isEmpty ? "❌ Not provided" : "✅ Configured")")
    }
    
    // MARK: - Configuration (Hardcoded)

    /// Get the service to use for a specific language pair (hardcoded configuration)
    private func getServiceForLanguagePair(from: TranslationLanguage, to: TranslationLanguage) -> TranslationServiceType {
        return currentConfig.serviceForLanguagePair(from: from, to: to)
    }
    
    // MARK: - Translation Methods
    
    /// Translates text using the configured service strategy
    func translate(
        text: String,
        from sourceLanguage: TranslationLanguage,
        to targetLanguage: TranslationLanguage
    ) async throws -> String {
        
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw TranslationError.emptyText
        }
        
        guard sourceLanguage != targetLanguage else {
            return text
        }
        
        // Determine which service to use
        let selectedService = getServiceForLanguagePair(from: sourceLanguage, to: targetLanguage)
        
        print("🔄 HybridTranslationService: Using \(selectedService.displayName) for \(sourceLanguage.displayName) → \(targetLanguage.displayName)")
        
        isTranslating = true
        defer { isTranslating = false }
        
        do {
            let translatedText: String

            switch selectedService {
            case .google:
                translatedText = try await performGoogleTranslation(
                    text: text,
                    source: sourceLanguage.rawValue,
                    target: targetLanguage.rawValue
                )
            case .googleCloud:
                translatedText = try await performGoogleCloudTranslation(
                    text: text,
                    source: sourceLanguage.rawValue,
                    target: targetLanguage.rawValue
                )
            case .nllb:
                translatedText = try await performNLLBTranslation(
                    text: text,
                    source: sourceLanguage,
                    target: targetLanguage
                )
            }
            
            print("✅ HybridTranslationService: Translation completed with \(selectedService.displayName): '\(translatedText)'")
            return translatedText
            
        } catch {
            print("⚠️ HybridTranslationService: \(selectedService.displayName) failed: \(error)")
            
            // Try fallback service if configured
            if let fallbackService = currentConfig.fallbackService, fallbackService != selectedService {
                print("🔄 HybridTranslationService: Trying fallback service: \(fallbackService.displayName)")
                
                do {
                    let fallbackText: String

                    switch fallbackService {
                    case .google:
                        fallbackText = try await performGoogleTranslation(
                            text: text,
                            source: sourceLanguage.rawValue,
                            target: targetLanguage.rawValue
                        )
                    case .googleCloud:
                        fallbackText = try await performGoogleCloudTranslation(
                            text: text,
                            source: sourceLanguage.rawValue,
                            target: targetLanguage.rawValue
                        )
                    case .nllb:
                        fallbackText = try await performNLLBTranslation(
                            text: text,
                            source: sourceLanguage,
                            target: targetLanguage
                        )
                    }
                    
                    print("✅ HybridTranslationService: Fallback \(fallbackService.displayName) succeeded: '\(fallbackText)'")
                    return fallbackText
                    
                } catch let fallbackError {
                    print("❌ HybridTranslationService: Fallback \(fallbackService.displayName) also failed: \(fallbackError)")
                    throw fallbackError
                }
            }
            
            throw error
        }
    }
    
    /// Translates text with auto-detection of source language
    func translateWithAutoDetection(
        text: String,
        to targetLanguage: TranslationLanguage
    ) async throws -> String {
        
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw TranslationError.emptyText
        }
        
        print("🔄 HybridTranslationService: Auto-translating '\(text)' to \(targetLanguage.displayName)")
        
        isTranslating = true
        defer { isTranslating = false }
        
        // For auto-detection, prefer Google Translate as it has better auto-detection
        do {
            let translatedText = try await performGoogleTranslation(
                text: text,
                source: "auto",
                target: targetLanguage.rawValue
            )
            
            print("✅ HybridTranslationService: Auto-translation completed: '\(translatedText)'")
            return translatedText
            
        } catch {
            print("❌ HybridTranslationService: Auto-translation failed: \(error)")
            throw error
        }
    }
    
    /// Detects the language of the given text
    func detectLanguage(text: String) async throws -> (language: TranslationLanguage, confidence: Double) {
        guard !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw TranslationError.emptyText
        }
        
        print("🔍 HybridTranslationService: Detecting language for '\(text)'")
        
        // Use Google Translate for language detection (better auto-detection)
        do {
            let response = try await performGoogleTranslationWithResponse(
                text: text,
                source: "auto",
                target: "en"
            )
            
            let detectedLanguageCode = response.src
            let translationLanguage = TranslationLanguage.allCases.first { $0.rawValue == detectedLanguageCode } ?? .english
            let confidence = response.confidence ?? 0.9
            
            print("✅ HybridTranslationService: Detected language: \(translationLanguage.displayName) (confidence: \(confidence))")
            return (language: translationLanguage, confidence: confidence)
            
        } catch {
            print("❌ HybridTranslationService: Language detection failed: \(error)")
            throw error
        }
    }
    
    // MARK: - Private Methods
    
    private func loadSavedPreferences() {
        // Load selected target language
        if let savedLanguageCode = UserDefaults.standard.string(forKey: "selectedTargetLanguage"),
           let savedLanguage = TranslationLanguage.allCases.first(where: { $0.rawValue == savedLanguageCode }) {
            selectedTargetLanguage = savedLanguage
        }

        // Load translation enabled state
        if UserDefaults.standard.object(forKey: "isTranslationEnabled") != nil {
            isTranslationEnabled = UserDefaults.standard.bool(forKey: "isTranslationEnabled")
        }

        // Configuration is hardcoded, no need to load service preferences
    }

    // MARK: - Google Translate Implementation

    private func performGoogleTranslation(text: String, source: String, target: String) async throws -> String {
        let response = try await performGoogleTranslationWithResponse(text: text, source: source, target: target)

        // Extract translated text from sentences
        let translatedText = response.sentences?
            .compactMap { $0.trans }
            .joined() ?? ""

        guard !translatedText.isEmpty else {
            throw TranslationError.invalidResponse
        }

        return translatedText
    }

    private func performGoogleTranslationWithResponse(text: String, source: String, target: String) async throws -> GoogleTranslateResponse {
        // Build Google Translate URL with query parameters (matching original implementation)
        var urlComponents = URLComponents(string: "https://translate.google.com/translate_a/single")!
        urlComponents.queryItems = [
            URLQueryItem(name: "client", value: "at"),
            URLQueryItem(name: "dt", value: "t"),    // return sentences
            URLQueryItem(name: "dt", value: "rm"),   // add translit to sentences
            URLQueryItem(name: "dj", value: "1")     // result as pretty json
        ]

        guard let url = urlComponents.url else {
            throw TranslationError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/x-www-form-urlencoded;charset=utf-8", forHTTPHeaderField: "Content-Type")

        // Build POST body parameters (matching original implementation)
        var bodyComponents = URLComponents()
        bodyComponents.queryItems = [
            URLQueryItem(name: "sl", value: source), // source language
            URLQueryItem(name: "tl", value: target), // target language
            URLQueryItem(name: "q", value: text)     // text to translate
        ]

        request.httpBody = bodyComponents.percentEncodedQuery?.data(using: .utf8)

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw TranslationError.invalidResponse
        }

        // Handle rate limiting
        if httpResponse.statusCode == 429 {
            let responseText = String(data: data, encoding: .utf8) ?? ""
            print("⚠️ Google Translate: Rate limited: \(responseText)")
            throw TranslationError.rateLimited
        }

        guard httpResponse.statusCode == 200 else {
            let responseText = String(data: data, encoding: .utf8) ?? ""
            print("❌ Google Translate: HTTP \(httpResponse.statusCode): \(responseText)")
            throw TranslationError.serverError(httpResponse.statusCode)
        }

        do {
            let translationResponse = try JSONDecoder().decode(GoogleTranslateResponse.self, from: data)
            return translationResponse
        } catch {
            print("❌ Google Translate: Failed to decode response: \(error)")
            print("Raw response: \(String(data: data, encoding: .utf8) ?? "Unable to decode")")
            throw TranslationError.decodingError(error)
        }
    }

    // MARK: - Google Cloud Translation Implementation

    private func performGoogleCloudTranslation(text: String, source: String, target: String) async throws -> String {
        do {
            let translatedText = try await googleCloudService.translate(
                text: text,
                from: source,
                to: target
            )
            return translatedText
        } catch let error as GoogleCloudTranslationError {
            // Convert Google Cloud errors to our standard translation errors
            switch error {
            case .emptyText:
                throw TranslationError.emptyText
            case .missingAPIKey:
                throw TranslationError.invalidURL // Treat as configuration error
            case .unauthorized(_):
                throw TranslationError.serverError(401)
            case .forbidden(_):
                throw TranslationError.serverError(403)
            case .rateLimited(_):
                throw TranslationError.rateLimited
            case .serverError(let code, _):
                throw TranslationError.serverError(code)
            case .decodingError(let decodingError):
                throw TranslationError.decodingError(decodingError)
            default:
                throw TranslationError.invalidResponse
            }
        } catch {
            throw error
        }
    }

    // MARK: - NLLB Implementation

    private func performNLLBTranslation(
        text: String,
        source: TranslationLanguage,
        target: TranslationLanguage
    ) async throws -> String {

        // Convert Google language codes to NLLB codes
        let nllbSource = NLLBLanguageMapper.toNLLB(source.rawValue)
        let nllbTarget = NLLBLanguageMapper.toNLLB(target.rawValue)

        // Check if both languages are supported by NLLB
        guard NLLBLanguageMapper.isNLLBSupported(source.rawValue) &&
              NLLBLanguageMapper.isNLLBSupported(target.rawValue) else {
            throw TranslationError.unsupportedLanguage("NLLB doesn't support \(source.displayName) → \(target.displayName)")
        }

        print("🔄 NLLB: Translating '\(text)' from \(nllbSource) to \(nllbTarget)")

        let url = URL(string: "\(currentConfig.nllbBaseURL)/api/v4/translator")!
        var urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false)!
        urlComponents.queryItems = [
            URLQueryItem(name: "text", value: text),
            URLQueryItem(name: "source", value: nllbSource),
            URLQueryItem(name: "target", value: nllbTarget)
        ]

        guard let finalURL = urlComponents.url else {
            throw TranslationError.invalidURL
        }

        var request = URLRequest(url: finalURL)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Accept")

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw TranslationError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            let responseText = String(data: data, encoding: .utf8) ?? ""
            print("❌ NLLB: HTTP \(httpResponse.statusCode): \(responseText)")
            throw TranslationError.serverError(httpResponse.statusCode)
        }

        do {
            let translationResponse = try JSONDecoder().decode(NLLBTranslationResponse.self, from: data)
            return translationResponse.result
        } catch {
            print("❌ NLLB: Failed to decode response: \(error)")
            print("Raw response: \(String(data: data, encoding: .utf8) ?? "Unable to decode")")
            throw TranslationError.decodingError(error)
        }
    }

    private func performNLLBLanguageDetection(text: String) async throws -> (language: TranslationLanguage, confidence: Double) {
        let url = URL(string: "\(currentConfig.nllbBaseURL)/api/v4/language")!
        var urlComponents = URLComponents(url: url, resolvingAgainstBaseURL: false)!
        urlComponents.queryItems = [
            URLQueryItem(name: "text", value: text)
        ]

        guard let finalURL = urlComponents.url else {
            throw TranslationError.invalidURL
        }

        var request = URLRequest(url: finalURL)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Accept")

        let (data, response) = try await session.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw TranslationError.invalidResponse
        }

        guard httpResponse.statusCode == 200 else {
            throw TranslationError.serverError(httpResponse.statusCode)
        }

        let detectionResponse = try JSONDecoder().decode(NLLBLanguageDetectionResponse.self, from: data)

        // Map NLLB language code to our TranslationLanguage enum
        let googleCode = NLLBLanguageMapper.toGoogle(detectionResponse.language)
        let translationLanguage = TranslationLanguage.allCases.first { $0.rawValue == googleCode } ?? .english

        return (language: translationLanguage, confidence: detectionResponse.confidence)
    }
}
